@echo off
chcp 65001 >nul
echo ========================================
echo       InStock 系统停止脚本
echo ========================================
echo.

echo 🛑 正在停止InStock相关进程...

echo [1/2] 查找Python Web服务进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| find "web_service.py"') do (
    echo 找到进程: %%i
    taskkill /pid %%i /f >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Web服务进程已停止
    )
)

echo [2/2] 查找其他Python相关进程...
tasklist /fi "imagename eq python.exe" /fo table | find "python.exe" >nul 2>&1
if not errorlevel 1 (
    echo 发现其他Python进程，是否全部停止？
    echo [Y] 是  [N] 否
    choice /c YN /n
    if errorlevel 2 goto :skip_kill
    if errorlevel 1 (
        taskkill /im python.exe /f >nul 2>&1
        echo ✅ 所有Python进程已停止
    )
)

:skip_kill
echo.
echo 🎯 InStock系统已停止
echo 💡 提示：如需重新启动，请运行 start_instock.bat
echo.
pause
