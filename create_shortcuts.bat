@echo off
chcp 65001 >nul
echo ========================================
echo       创建桌面快捷方式
echo ========================================
echo.

set "desktop=%USERPROFILE%\Desktop"
set "current_dir=%~dp0"

echo 🔗 正在创建桌面快捷方式...

REM 创建InStock管理器快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\CreateShortcut.vbs"
echo sLinkFile = "%desktop%\InStock管理器.lnk" >> "%temp%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\CreateShortcut.vbs"
echo oLink.TargetPath = "%current_dir%instock_manager.bat" >> "%temp%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%current_dir%" >> "%temp%\CreateShortcut.vbs"
echo oLink.Description = "InStock股票系统管理器" >> "%temp%\CreateShortcut.vbs"
echo oLink.Save >> "%temp%\CreateShortcut.vbs"
cscript "%temp%\CreateShortcut.vbs" >nul 2>&1

REM 创建快速启动快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\CreateShortcut2.vbs"
echo sLinkFile = "%desktop%\启动InStock.lnk" >> "%temp%\CreateShortcut2.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\CreateShortcut2.vbs"
echo oLink.TargetPath = "%current_dir%start_instock.bat" >> "%temp%\CreateShortcut2.vbs"
echo oLink.WorkingDirectory = "%current_dir%" >> "%temp%\CreateShortcut2.vbs"
echo oLink.Description = "一键启动InStock股票系统" >> "%temp%\CreateShortcut2.vbs"
echo oLink.Save >> "%temp%\CreateShortcut2.vbs"
cscript "%temp%\CreateShortcut2.vbs" >nul 2>&1

REM 创建Web访问快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\CreateShortcut3.vbs"
echo sLinkFile = "%desktop%\InStock网页版.lnk" >> "%temp%\CreateShortcut3.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\CreateShortcut3.vbs"
echo oLink.TargetPath = "%current_dir%open_instock.bat" >> "%temp%\CreateShortcut3.vbs"
echo oLink.WorkingDirectory = "%current_dir%" >> "%temp%\CreateShortcut3.vbs"
echo oLink.Description = "打开InStock网页界面" >> "%temp%\CreateShortcut3.vbs"
echo oLink.Save >> "%temp%\CreateShortcut3.vbs"
cscript "%temp%\CreateShortcut3.vbs" >nul 2>&1

REM 清理临时文件
del "%temp%\CreateShortcut.vbs" >nul 2>&1
del "%temp%\CreateShortcut2.vbs" >nul 2>&1
del "%temp%\CreateShortcut3.vbs" >nul 2>&1

echo ✅ 桌面快捷方式创建完成！
echo.
echo 已创建以下快捷方式：
echo   📋 InStock管理器.lnk - 系统管理界面
echo   🚀 启动InStock.lnk - 一键启动系统
echo   🌐 InStock网页版.lnk - 快速打开网页
echo.
echo 💡 提示：双击桌面上的快捷方式即可使用
echo.
pause
