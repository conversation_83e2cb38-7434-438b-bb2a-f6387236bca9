@echo off
title InStock Menu

:start
cls
echo ========================================
echo       InStock 系统菜单
echo ========================================
echo.
echo 1. 快速启动系统
echo 2. 打开网页 (http://localhost:9988)
echo 3. 更新数据
echo 4. 查看状态
echo 5. 退出
echo.
set /p choice=请选择 (1-5): 

if "%choice%"=="1" goto option1
if "%choice%"=="2" goto option2
if "%choice%"=="3" goto option3
if "%choice%"=="4" goto option4
if "%choice%"=="5" goto option5
goto start

:option1
echo.
echo 启动系统...
call quick_start.bat
pause
goto start

:option2
echo.
echo 打开网页...
start "" "http://localhost:9988"
echo 网页已在浏览器中打开
pause
goto start

:option3
echo.
echo 更新数据...
call update_data.bat
pause
goto start

:option4
echo.
echo 系统状态:
echo.
"C:\Python311\python.exe" --version 2>nul && echo Python: 正常 || echo Python: 异常
netstat -an | find "3306" >nul 2>&1 && echo MySQL: 正常 || echo MySQL: 未启动
netstat -an | find "9988" >nul 2>&1 && echo Web服务: 运行中 || echo Web服务: 未运行
echo.
pause
goto start

:option5
echo.
echo 再见!
exit
