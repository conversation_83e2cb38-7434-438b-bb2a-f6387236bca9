# InStock PowerShell启动脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "       InStock 系统启动" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python
Write-Host "[1/3] 检查Python环境..." -ForegroundColor Yellow
try {
    $pythonVersion = & "C:\Python311\python.exe" --version 2>$null
    Write-Host "✓ Python环境正常: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Python 3.11 未找到" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 检查MySQL
Write-Host "[2/3] 检查MySQL服务..." -ForegroundColor Yellow
$mysqlRunning = Get-NetTCPConnection -LocalPort 3306 -ErrorAction SilentlyContinue
if ($mysqlRunning) {
    Write-Host "✓ MySQL服务正常" -ForegroundColor Green
} else {
    Write-Host "✗ MySQL服务未启动，请启动XAMPP中的MySQL" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 检查数据库
Write-Host "[3/3] 检查数据库连接..." -ForegroundColor Yellow
try {
    & "C:\Python311\python.exe" -c "import pymysql; conn = pymysql.connect(host='localhost', user='root', password='', port=3306, database='instockdb'); conn.close()" 2>$null
    Write-Host "✓ 数据库连接正常" -ForegroundColor Green
} catch {
    Write-Host "! 数据库连接失败，正在初始化..." -ForegroundColor Yellow
    & "C:\Python311\python.exe" "instock\job\init_job.py"
    Write-Host "✓ 数据库初始化完成" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "正在启动Web服务..." -ForegroundColor Cyan
Write-Host "访问地址: http://localhost:9988" -ForegroundColor Green
Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 启动Web服务
Set-Location "instock\web"
& "C:\Python311\python.exe" "web_service.py"

Write-Host ""
Write-Host "服务已停止" -ForegroundColor Yellow
Read-Host "按回车键退出"
