@echo off
chcp 65001 >nul
echo ========================================
echo       InStock 股票系统启动脚本
echo ========================================
echo.

echo [1/4] 检查Python环境...
"C:\Python311\python.exe" --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 3.11 未找到，请检查安装路径
    pause
    exit /b 1
)
echo ✅ Python环境正常

echo.
echo [2/4] 检查MySQL服务...
netstat -an | find "3306" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL服务未启动，请先启动XAMPP中的MySQL服务
    echo 💡 提示：打开XAMPP控制面板，点击MySQL的Start按钮
    pause
    exit /b 1
)
echo ✅ MySQL服务正常

echo.
echo [3/4] 测试数据库连接...
"C:\Python311\python.exe" -c "import pymysql; conn = pymysql.connect(host='localhost', user='root', password='', port=3306, database='instockdb'); conn.close(); print('数据库连接成功')" 2>nul
if errorlevel 1 (
    echo ❌ 数据库连接失败，正在尝试初始化数据库...
    "C:\Python311\python.exe" instock\job\init_job.py
    if errorlevel 1 (
        echo ❌ 数据库初始化失败
        pause
        exit /b 1
    )
    echo ✅ 数据库初始化完成
) else (
    echo ✅ 数据库连接正常
)

echo.
echo [4/4] 启动Web服务...
echo 🚀 正在启动InStock系统...
echo 📱 Web地址: http://localhost:9988
echo 🛑 按 Ctrl+C 可停止服务
echo.

cd /d "%~dp0\instock\web"
"C:\Python311\python.exe" web_service.py

echo.
echo 服务已停止
pause
