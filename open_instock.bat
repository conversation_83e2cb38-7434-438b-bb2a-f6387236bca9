@echo off
chcp 65001 >nul
echo ========================================
echo       InStock 快速访问
echo ========================================
echo.

echo 🌐 正在打开InStock系统...
echo 📱 Web地址: http://localhost:9988
echo.

REM 检查服务是否运行
netstat -an | find "9988" >nul 2>&1
if errorlevel 1 (
    echo ❌ InStock服务未运行
    echo 💡 请先运行 start_instock.bat 启动系统
    echo.
    echo 是否现在启动系统？
    echo [Y] 是  [N] 否
    choice /c YN /n
    if errorlevel 2 goto :end
    if errorlevel 1 (
        echo 🚀 正在启动系统...
        start "" "%~dp0start_instock.bat"
        timeout /t 5 >nul
    )
)

echo ✅ 正在打开浏览器...
start "" "http://localhost:9988"

:end
echo.
echo 🎯 完成！
timeout /t 2 >nul
