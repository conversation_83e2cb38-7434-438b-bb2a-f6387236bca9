@echo off
title InStock System Manager

:menu
cls
echo ========================================
echo       InStock 系统管理器
echo ========================================
echo.
echo 请选择操作：
echo.
echo [1] 启动InStock系统
echo [2] 打开Web界面
echo [3] 更新股票数据
echo [4] 停止系统服务
echo [5] 查看系统状态
echo [0] 退出
echo.
echo ========================================

set /p choice=请输入选项 (0-5): 

if "%choice%"=="1" goto start_system
if "%choice%"=="2" goto open_web
if "%choice%"=="3" goto update_data
if "%choice%"=="4" goto stop_system
if "%choice%"=="5" goto check_status
if "%choice%"=="0" goto exit

echo 无效选项，请重新选择
pause
goto menu

:start_system
echo.
echo 正在启动InStock系统...
echo 请确保XAMPP中的MySQL服务已启动
echo.
pause
call start_instock.bat
pause
goto menu

:open_web
echo.
echo 正在打开Web界面...
start "" "http://localhost:9988"
echo 如果页面无法打开，请先启动系统
pause
goto menu

:update_data
echo.
echo 正在更新股票数据...
echo 这可能需要几分钟时间，请耐心等待
echo.
pause
call update_data.bat
pause
goto menu

:stop_system
echo.
echo 正在停止系统服务...
taskkill /f /im python.exe >nul 2>&1
echo 系统已停止
pause
goto menu

:check_status
cls
echo ========================================
echo       系统状态检查
echo ========================================
echo.

echo [Python环境]
"C:\Python311\python.exe" --version 2>nul
if errorlevel 1 (
    echo 错误: Python 3.11 未找到
) else (
    echo 正常: Python环境正常
)

echo.
echo [MySQL服务]
netstat -an | find "3306" >nul 2>&1
if errorlevel 1 (
    echo 错误: MySQL服务未启动
    echo 请启动XAMPP中的MySQL服务
) else (
    echo 正常: MySQL服务正常
)

echo.
echo [Web服务]
netstat -an | find "9988" >nul 2>&1
if errorlevel 1 (
    echo 错误: Web服务未运行
    echo 请先启动InStock系统
) else (
    echo 正常: Web服务正常运行
    echo 访问地址: http://localhost:9988
)

echo.
pause
goto menu

:exit
echo.
echo 感谢使用InStock系统！
timeout /t 2 >nul
exit
