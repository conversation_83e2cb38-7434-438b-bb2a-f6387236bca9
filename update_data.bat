@echo off
chcp 65001 >nul
echo ========================================
echo       InStock 数据更新脚本
echo ========================================
echo.

echo [1/3] 检查系统环境...
"C:\Python311\python.exe" --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 3.11 未找到
    pause
    exit /b 1
)

netstat -an | find "3306" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL服务未启动，请先启动XAMPP
    pause
    exit /b 1
)
echo ✅ 系统环境正常

echo.
echo [2/3] 开始基础数据抓取...
echo 📊 正在获取股票基础数据，请耐心等待...
cd /d "%~dp0"
"C:\Python311\python.exe" instock\job\basic_data_daily_job.py
if errorlevel 1 (
    echo ❌ 基础数据抓取失败
    pause
    exit /b 1
)
echo ✅ 基础数据抓取完成

echo.
echo [3/3] 开始完整数据处理...
echo 🔄 正在处理技术指标和分析数据，请耐心等待...
"C:\Python311\python.exe" instock\job\execute_daily_job.py
if errorlevel 1 (
    echo ❌ 数据处理失败
    pause
    exit /b 1
)
echo ✅ 数据处理完成

echo.
echo 🎉 数据更新完成！
echo 💡 提示：建议每个交易日收盘后运行此脚本更新数据
echo.
pause
