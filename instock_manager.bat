@echo off
title InStock System Manager

:menu
cls
echo ========================================
echo       InStock 系统管理器
echo ========================================
echo.
echo 请选择操作：
echo.
echo [1] 启动InStock系统
echo [2] 打开Web界面 (http://localhost:9988)
echo [3] 更新股票数据
echo [4] 停止系统服务
echo [5] 查看系统状态
echo [6] 打开项目文件夹
echo [7] 查看使用说明
echo [0] 退出
echo.
echo ========================================

set /p choice=请输入选项 (0-7):

if "%choice%"=="1" goto start_system
if "%choice%"=="2" goto open_web
if "%choice%"=="3" goto update_data
if "%choice%"=="4" goto stop_system
if "%choice%"=="5" goto check_status
if "%choice%"=="6" goto open_folder
if "%choice%"=="7" goto show_help
if "%choice%"=="0" goto exit
goto menu

:start_system
echo.
echo 正在启动InStock系统...
call start_instock.bat
goto menu

:open_web
echo.
echo 正在打开Web界面...
call open_instock.bat
goto menu

:update_data
echo.
echo 正在更新股票数据...
call update_data.bat
goto menu

:stop_system
echo.
echo 正在停止系统服务...
call stop_instock.bat
goto menu

:check_status
cls
echo ========================================
echo       系统状态检查
echo ========================================
echo.

echo [Python环境]
"C:\Python311\python.exe" --version 2>nul
if errorlevel 1 (
    echo ❌ Python 3.11 未找到
) else (
    echo ✅ Python环境正常
)

echo.
echo [MySQL服务]
netstat -an | find "3306" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL服务未启动
) else (
    echo ✅ MySQL服务正常
)

echo.
echo [Web服务]
netstat -an | find "9988" >nul 2>&1
if errorlevel 1 (
    echo ❌ Web服务未运行
) else (
    echo ✅ Web服务正常运行 (http://localhost:9988)
)

echo.
echo [数据库连接]
"C:\Python311\python.exe" -c "import pymysql; conn = pymysql.connect(host='localhost', user='root', password='', port=3306, database='instockdb'); conn.close(); print('✅ 数据库连接正常')" 2>nul
if errorlevel 1 (
    echo ❌ 数据库连接失败
)

echo.
pause
goto menu

:open_folder
echo.
echo 📁 打开项目文件夹...
start "" "%~dp0"
goto menu

:show_help
cls
echo ========================================
echo       InStock 使用说明
echo ========================================
echo.
echo 📖 快速开始：
echo   1. 确保XAMPP中的MySQL服务已启动
echo   2. 运行"启动InStock系统"
echo   3. 打开浏览器访问 http://localhost:9988
echo.
echo 📊 数据更新：
echo   - 建议每个交易日收盘后运行"更新股票数据"
echo   - 首次使用需要更新数据才能看到股票信息
echo.
echo 🔧 常见问题：
echo   - 如果启动失败，请检查XAMPP MySQL是否启动
echo   - 如果数据为空，请先运行数据更新
echo   - 如果端口冲突，请检查9988端口是否被占用
echo.
echo 📞 技术支持：
echo   - 项目地址：https://github.com/myhhub/stock
echo   - 文档地址：README.md
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用InStock系统！
timeout /t 2 >nul
exit
