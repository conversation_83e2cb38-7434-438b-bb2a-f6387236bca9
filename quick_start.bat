@echo off
title InStock Quick Start

echo ========================================
echo       InStock 快速启动
echo ========================================
echo.

echo 正在检查环境...

REM 检查Python
echo [1/3] 检查Python环境...
"C:\Python311\python.exe" --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python 3.11
    echo 请确保Python 3.11安装在 C:\Python311\
    pause
    exit /b 1
)
echo 成功: Python环境正常

REM 检查MySQL
echo [2/3] 检查MySQL服务...
netstat -an | find "3306" >nul 2>&1
if errorlevel 1 (
    echo 错误: MySQL服务未启动
    echo 请打开XAMPP控制面板，启动MySQL服务
    echo 然后重新运行此脚本
    pause
    exit /b 1
)
echo 成功: MySQL服务正常

REM 检查数据库
echo [3/3] 检查数据库连接...
"C:\Python311\python.exe" -c "import pymysql; conn = pymysql.connect(host='localhost', user='root', password='', port=3306, database='instockdb'); conn.close()" 2>nul
if errorlevel 1 (
    echo 警告: 数据库连接失败，正在初始化...
    "C:\Python311\python.exe" instock\job\init_job.py
    if errorlevel 1 (
        echo 错误: 数据库初始化失败
        pause
        exit /b 1
    )
    echo 成功: 数据库初始化完成
) else (
    echo 成功: 数据库连接正常
)

echo.
echo ========================================
echo 环境检查完成，正在启动系统...
echo ========================================
echo.

echo 启动Web服务...
echo 访问地址: http://localhost:9988
echo 按 Ctrl+C 可停止服务
echo.

cd /d "%~dp0\instock\web"
"C:\Python311\python.exe" web_service.py

echo.
echo 服务已停止
pause
